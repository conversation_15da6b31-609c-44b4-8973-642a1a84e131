{"name": "uix", "version": "0.1.7", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "prestart": "graphql-codegen", "start": "react-native start", "test": "jest", "compile": "graphql-codegen", "watch": "graphql-codegen -w", "postinstall": "patch-package", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "lint:all": "npm run lint && npm run format", "deeplink": "node package-deep-link-script.js", "deeplink:drops": "node package-deep-link-script.js drops", "deeplink:playbook": "node package-deep-link-script.js playbook"}, "dependencies": {"@apollo/client": "^3.12.4", "@code-push-next/react-native-code-push": "^10.0.1", "@gorhom/bottom-sheet": "^5.0.6", "@gorhom/portal": "^1.0.14", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/blur": "^4.4.1", "@react-native-documents/picker": "^10.1.2", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@reduxjs/toolkit": "^2.5.0", "@sentry/react-native": "^6.9.1", "@shopify/flash-list": "^1.7.2", "@shopify/react-native-performance": "^4.1.2", "add": "^2.0.6", "appsonair-react-native-appsync": "^0.2.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "graphql": "^16.10.0", "immer": "^10.1.1", "install": "^0.13.0", "lodash": "^4.17.21", "mixpanel-react-native": "^3.0.8", "moti": "^0.29.0", "npm": "^11.0.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-bootsplash": "^6.3.4", "react-native-countdown-timer-hooks": "^1.0.5", "react-native-device-info": "^14.0.4", "react-native-encrypted-storage": "^4.0.3", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.21.2", "react-native-gifted-charts": "^1.4.58", "react-native-keyboard-controller": "^1.16.6", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-moengage": "^11.1.3", "react-native-network-logger": "^1.17.0", "react-native-otp-entry": "^1.8.2", "react-native-paper": "^5.13.1", "react-native-reanimated": "^3.16.6", "react-native-reanimated-carousel": "^4.0.0", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "^4.4.0", "react-native-shimmer-placeholder": "^2.0.9", "react-native-story-view": "^3.2.0", "react-native-svg": "^15.10.1", "react-native-timer-picker": "^2.2.0", "react-native-toast-notifications": "^3.4.0", "react-native-ui-datepicker": "^3.1.2", "react-native-unistyles": "^2.31.0", "react-native-video": "^6.11.0", "react-native-video-cache": "^2.7.4", "react-native-video-cache-control": "^1.2.3", "react-redux": "^9.2.0", "reactotron-react-native": "^5.1.13", "redux-persist": "^6.0.0", "sp-react-native-in-app-updates": "^1.4.1", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@eslint/js": "^9.17.0", "@graphql-codegen/cli": "^5.0.3", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.9", "@react-native/metro-config": "0.76.9", "@react-native/typescript-config": "0.76.9", "@types/lodash": "^4.17.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^15.14.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "^3.4.2", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "5.4.5", "typescript-eslint": "^8.18.1"}, "engines": {"node": ">=18"}}