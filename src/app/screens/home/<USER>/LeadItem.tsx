import {getCustomRelativeTime} from '@utils/DateTimeUtils';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React, {useMemo} from 'react';
import {Image, Pressable, StyleSheet, View} from 'react-native';

import {leadStatusColors, leadStatusIcons} from './utils';

import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixText} from '@/app/ui/UixText';
import {
  Leads_Lead_Details,
  Leads_Lead_Status_Enum,
} from '@/types/__generated__/graphql';

dayjs.extend(relativeTime);

interface LeadItemProps {
  lead: Leads_Lead_Details;
  onPress: (lead: Leads_Lead_Details) => void;
}

export const LeadItem = ({lead, onPress}: LeadItemProps) => {
  const fullName = useMemo(() => {
    const capitalize = (str?: string | null) =>
      str ? str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() : '';
    return `${capitalize(lead?.first_name)} ${capitalize(lead?.last_name)}`.trim();
  }, [lead?.first_name, lead?.last_name]);

  const status = (lead?.lead_status || 'CREATED') as Leads_Lead_Status_Enum;
  const statusColors = leadStatusColors[status];

  const formattedSource = useMemo(() => {
    if (!lead.source) return '';
    return lead.source.toLowerCase().split('_').join(' ');
  }, [lead.source]);

  const formattedStatus = useMemo(() => {
    return status.split('_').join(' ') || 'N/A';
  }, [status]);

  const lastUpdateText = useMemo(() => {
    if (!lead.updated_at) return 'Last update: N/A';
    return `Last update: ${getCustomRelativeTime(lead.updated_at)}`;
  }, [lead.updated_at]);

  const getStatusContainerStyle = () => ({
    ...styles.textContainer,
    backgroundColor: statusColors.background,
  });

  const getStatusTextStyle = () => ({
    ...styles.text,
    color: statusColors.text,
  });

  if (!lead) {
    return null;
  }

  return (
    <Pressable style={styles.container} onPress={() => onPress(lead)}>
      <View>
        {lead.lead_company && (
          <View style={styles.content}>
            <UixText variant="dotsBold" style={styles.leadCompany}>
              {lead.lead_company || ''}
            </UixText>
          </View>
        )}
        <View style={styles.leftContainer}>
          {fullName && <UixText style={styles.name}>{fullName}</UixText>}
          <View style={styles.footer}>
            <View style={getStatusContainerStyle()}>
              <Image
                source={leadStatusIcons[status]}
                style={styles.statusIcon}
              />
              <UixText
                variant="dotsBold"
                style={getStatusTextStyle()}
                numberOfLines={2}>
                {formattedStatus}
              </UixText>
            </View>
          </View>

          {lead.updated_at && (
            <View style={styles.footer}>
              <View style={styles.textContainer}>
                <UixText
                  variant="dotsBold"
                  style={styles.text}
                  numberOfLines={2}>
                  {lastUpdateText}
                </UixText>
              </View>
            </View>
          )}
        </View>
      </View>

      <View style={styles.rightContainer}>
        <Image
          source={require('@assets/right-arrow.png')}
          style={styles.arrowIcon}
        />
        <View style={styles.nameContainer}>
          <View style={styles.leadTypeContainer}>
            {lead.lead_type && (
              <UixText style={styles.leadType}>
                {lead.lead_type + '|' || ''}
              </UixText>
            )}
            {lead.source && (
              <UixText style={styles.leadType}>{formattedSource}</UixText>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    marginVertical: 8,
    padding: 16,
    backgroundColor: updateColorWithOpacity(Colors.Teal425, 0.08),
    borderWidth: 1,
    borderColor: updateColorWithOpacity(Colors.Teal425, 0.2),
    gap: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  content: {
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leadTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: updateColorWithOpacity(Colors.Teal425, 0.12),
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 4,
  },
  leadType: {
    fontSize: 12,
    color: Colors.White,
    padding: 4,
  },
  leadCompany: {
    fontSize: 18,
    lineHeight: 24,
    color: Colors.Teal400,
    marginBottom: 4,
    fontWeight: 700,
  },
  name: {
    fontSize: 16,
    lineHeight: 20,
    color: Colors.text,
    fontWeight: 500,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
  },
  textContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
    borderRadius: 6,
    gap: 4,
    backgroundColor: updateColorWithOpacity(Colors.Teal400, 0.1),
  },
  statusIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 2,
  },
  text: {
    fontSize: 14,
    color: updateColorWithOpacity(Colors.Teal425, 0.9),
  },
  arrowIcon: {
    width: 24,
    height: 24,
  },
  rightContainer: {
    alignItems: 'flex-end',
    gap: 8,
  },
  leftContainer: {
    gap: 8,
  },
});
